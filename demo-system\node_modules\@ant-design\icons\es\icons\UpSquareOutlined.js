import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UpSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/UpSquareOutlined";
import AntdIcon from "../components/AntdIcon";
var UpSquareOutlined = function UpSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: UpSquareOutlinedSvg
  }));
};

/**![up-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMzNCA2MjRoNDYuOWMxMC4yIDAgMTkuOS00LjkgMjUuOS0xMy4yTDUxMiA0NjUuNGwxMDUuMiAxNDUuNGM2IDguMyAxNS42IDEzLjIgMjUuOSAxMy4ySDY5MGM2LjUgMCAxMC4zLTcuNCA2LjUtMTIuN2wtMTc4LTI0NmE3Ljk1IDcuOTUgMCAwMC0xMi45IDBsLTE3OCAyNDZBNy45NiA3Ljk2IDAgMDAzMzQgNjI0eiIgLz48cGF0aCBkPSJNODgwIDExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjczNmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNzI4SDE4NFYxODRoNjU2djY1NnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(UpSquareOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpSquareOutlined';
}
export default RefIcon;