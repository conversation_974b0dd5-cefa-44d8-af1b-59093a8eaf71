import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["children"];
import useMemo from "rc-util/es/hooks/useMemo";
import isEqual from "rc-util/es/isEqual";
import * as React from 'react';
import CacheEntity from "./Cache";
export var ATTR_TOKEN = 'data-token-hash';
export var ATTR_MARK = 'data-css-hash';
export var ATTR_CACHE_PATH = 'data-cache-path';

// Mark css-in-js instance in style element
export var CSS_IN_JS_INSTANCE = '__cssinjs_instance__';
export function createCache() {
  var cssinjsInstanceId = Math.random().toString(12).slice(2);

  // Tricky SSR: Move all inline style to the head.
  // PS: We do not recommend tricky mode.
  if (typeof document !== 'undefined' && document.head && document.body) {
    var styles = document.body.querySelectorAll("style[".concat(ATTR_MARK, "]")) || [];
    var firstChild = document.head.firstChild;
    Array.from(styles).forEach(function (style) {
      style[CSS_IN_JS_INSTANCE] = style[CSS_IN_JS_INSTANCE] || cssinjsInstanceId;

      // Not force move if no head
      if (style[CSS_IN_JS_INSTANCE] === cssinjsInstanceId) {
        document.head.insertBefore(style, firstChild);
      }
    });

    // Deduplicate of moved styles
    var styleHash = {};
    Array.from(document.querySelectorAll("style[".concat(ATTR_MARK, "]"))).forEach(function (style) {
      var hash = style.getAttribute(ATTR_MARK);
      if (styleHash[hash]) {
        if (style[CSS_IN_JS_INSTANCE] === cssinjsInstanceId) {
          var _style$parentNode;
          (_style$parentNode = style.parentNode) === null || _style$parentNode === void 0 || _style$parentNode.removeChild(style);
        }
      } else {
        styleHash[hash] = true;
      }
    });
  }
  return new CacheEntity(cssinjsInstanceId);
}
var StyleContext = /*#__PURE__*/React.createContext({
  hashPriority: 'low',
  cache: createCache(),
  defaultCache: true
});
export var StyleProvider = function StyleProvider(props) {
  var children = props.children,
    restProps = _objectWithoutProperties(props, _excluded);
  var parentContext = React.useContext(StyleContext);
  var context = useMemo(function () {
    var mergedContext = _objectSpread({}, parentContext);
    Object.keys(restProps).forEach(function (key) {
      var value = restProps[key];
      if (restProps[key] !== undefined) {
        mergedContext[key] = value;
      }
    });
    var cache = restProps.cache;
    mergedContext.cache = mergedContext.cache || createCache();
    mergedContext.defaultCache = !cache && parentContext.defaultCache;
    return mergedContext;
  }, [parentContext, restProps], function (prev, next) {
    return !isEqual(prev[0], next[0], true) || !isEqual(prev[1], next[1], true);
  });
  return /*#__PURE__*/React.createElement(StyleContext.Provider, {
    value: context
  }, children);
};
export default StyleContext;