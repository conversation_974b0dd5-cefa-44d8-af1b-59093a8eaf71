import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import XOutlinedSvg from "@ant-design/icons-svg/es/asn/XOutlined";
import AntdIcon from "../components/AntdIcon";
var XOutlined = function XOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: XOutlinedSvg
  }));
};

/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOTIxIDkxMkw2MDEuMTEgNDQ1Ljc1bC41NS40M0w4OTAuMDggMTEySDc5My43TDU1OC43NCAzODQgMzcyLjE1IDExMkgxMTkuMzdsMjk4LjY1IDQzNS4zMS0uMDQtLjA0TDEwMyA5MTJoOTYuMzlMNDYwLjYgNjA5LjM4IDY2OC4yIDkxMnpNMzMzLjk2IDE4NC43M2w0NDguODMgNjU0LjU0SDcwNi40TDI1Ny4yIDE4NC43M3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(XOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'XOutlined';
}
export default RefIcon;