import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TwitchFilledSvg from "@ant-design/icons-svg/es/asn/TwitchFilled";
import AntdIcon from "../components/AntdIcon";
var TwitchFilled = function TwitchFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TwitchFilledSvg
  }));
};

/**![twitch](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48ZmlsdGVyIGZpbHRlclVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgaGVpZ2h0PSIxMDIuMyUiIGlkPSJhIiB3aWR0aD0iMTAyLjMlIiB4PSItMS4yJSIgeT0iLTEuMiUiPjxmZU9mZnNldCBkeT0iMiIgaW49IlNvdXJjZUFscGhhIiByZXN1bHQ9InNoYWRvd09mZnNldE91dGVyMSIgLz48ZmVHYXVzc2lhbkJsdXIgaW49InNoYWRvd09mZnNldE91dGVyMSIgcmVzdWx0PSJzaGFkb3dCbHVyT3V0ZXIxIiBzdGREZXZpYXRpb249IjIiIC8+PGZlQ29sb3JNYXRyaXggaW49InNoYWRvd0JsdXJPdXRlcjEiIHJlc3VsdD0ic2hhZG93TWF0cml4T3V0ZXIxIiB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuNSAwIiAvPjxmZU1lcmdlPjxmZU1lcmdlTm9kZSBpbj0ic2hhZG93TWF0cml4T3V0ZXIxIiAvPjxmZU1lcmdlTm9kZSBpbj0iU291cmNlR3JhcGhpYyIgLz48L2ZlTWVyZ2U+PC9maWx0ZXI+PC9kZWZzPjxnIGZpbHRlcj0idXJsKCNhKSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoOSA5KSI+PHBhdGggZD0iTTE4NS4xNCAxMTJMMTI4IDI1NC44NlY3OTcuN2gxNzEuNDNWOTEySDQxMy43TDUyOCA3OTcuNzFoMTQyLjg2bDIwMC0yMDBWMTEyem0zMTQuMjkgNDI4LjU3SDQxMy43VjMxMC4yMWg4NS43MnptMjAwIDBINjEzLjdWMzEwLjIxaDg1LjcyeiIgLz48L2c+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(TwitchFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TwitchFilled';
}
export default RefIcon;