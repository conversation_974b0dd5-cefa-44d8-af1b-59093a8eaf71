{"hash": "7e283157", "configHash": "7fd78b2e", "lockfileHash": "58fc7c38", "browserHash": "defe40ef", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c59d7139", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "7b8f1cd8", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "6299a59a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f7fa8f7b", "needsInterop": true}, "@ant-design/icons": {"src": "../../@ant-design/icons/es/index.js", "file": "@ant-design_icons.js", "fileHash": "7446f9d8", "needsInterop": false}, "antd": {"src": "../../antd/es/index.js", "file": "antd.js", "fileHash": "776d727a", "needsInterop": false}, "antd/locale/zh_CN": {"src": "../../antd/locale/zh_CN.js", "file": "antd_locale_zh_CN.js", "fileHash": "ab2fe1e5", "needsInterop": true}, "dayjs": {"src": "../../dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "32936192", "needsInterop": true}, "dayjs/locale/zh-cn": {"src": "../../dayjs/locale/zh-cn.js", "file": "dayjs_locale_zh-cn.js", "fileHash": "7938bc71", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d5e6b696", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "8b01c338", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "5652d07d", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "86ea9d6f", "needsInterop": false}}, "chunks": {"chunk-W36ZNOZU": {"file": "chunk-W36ZNOZU.js"}, "chunk-WAVGCWYK": {"file": "chunk-WAVGCWYK.js"}, "chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}